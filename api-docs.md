# ATMA Backend API Documentation

## Overview
ATMA (AI-Driven Talent Mapping Assessment) Backend terdiri dari 3 microservices:
- **Auth Service** (Port 3001): Autentikasi dan manajemen user/admin
- **Assessment Service** (Port 3003): Pemrosesan assessment psikologi dengan AI
- **Archive Service** (Port 3002): Penyimpanan dan manajemen hasil analisis

**Base URLs**:
- Auth Service: `http://localhost:3001`
- Assessment Service: `http://localhost:3003`
- Archive Service: `http://localhost:3002`

---

## Global Error Codes
- **400**: Bad Request - Validation error atau parameter tidak valid
- **401**: Unauthorized - Token tidak valid atau tidak ada
- **403**: Forbidden - Tidak memiliki permission untuk akses resource
- **404**: Not Found - Resource tidak ditemukan
- **409**: Conflict - Data sudah ada (misal email sudah terdaftar)
- **500**: Internal Server Error - Error pada server
- **503**: Service Unavailable - Service temporarily unavailable

---

## Authentication Headers

### User Authentication
```
Authorization: Bearer <jwt_token>
```

### Admin Authentication
```
Authorization: Bearer <admin_jwt_token>
```

### Internal Service Authentication
```
X-Service-Key: <internal_service_key>
X-Internal-Service: true
```

---

# Auth Service API

## Public Endpoints (Tidak perlu authentication)

### POST /auth/register
**Deskripsi**: Registrasi user baru
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    },
    "token": "jwt_token_here"
  }
}
```

### POST /auth/register/batch
**Deskripsi**: Registrasi batch user (untuk testing/seeding)
**Request Body**: Array of user objects
**Response**: Batch processing result

### POST /auth/login
**Deskripsi**: Login user
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    },
    "token": "jwt_token_here"
  }
}
```

## Protected User Endpoints (Perlu authentication token)

### GET /auth/profile
**Deskripsi**: Mendapatkan profil user yang sedang login
**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "full_name": "John Doe",
      "school_origin": "SMA Negeri 1",
      "date_of_birth": "2000-01-01",
      "gender": "male",
      "token_balance": 100
    }
  }
}
```

### PUT /auth/profile
**Deskripsi**: Update profil user
**Request Body**:
```json
{
  "full_name": "John Doe",
  "school_origin": "SMA Negeri 1",
  "date_of_birth": "2000-01-01",
  "gender": "male"
}
```

### POST /auth/change-password
**Deskripsi**: Ganti password user
**Request Body**:
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "newpassword123"
}
```

### POST /auth/logout
**Deskripsi**: Logout user (invalidate token di client side)

### GET /auth/token-balance
**Deskripsi**: Mendapatkan saldo token user
**Response**:
```json
{
  "success": true,
  "data": {
    "user_id": "uuid",
    "token_balance": 100
  }
}
```

## Admin Auth Endpoints

### POST /admin/login
**Deskripsi**: Login admin
**Request Body**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "admin": {
      "id": "uuid",
      "username": "admin",
      "email": "<EMAIL>",
      "role": "superadmin"
    },
    "token": "jwt_token_here"
  }
}
```

### GET /admin/profile
**Deskripsi**: Mendapatkan profil admin yang sedang login

### PUT /admin/profile
**Deskripsi**: Update profil admin

### POST /admin/change-password
**Deskripsi**: Ganti password admin

### POST /admin/logout
**Deskripsi**: Logout admin

### POST /admin/register (Superadmin only)
**Deskripsi**: Registrasi admin baru (hanya superadmin)
**Request Body**:
```json
{
  "username": "newadmin",
  "email": "<EMAIL>",
  "password": "NewPassword123!",
  "full_name": "New Admin",
  "role": "admin"
}
```

## Internal Service Endpoints

### POST /auth/verify-token
**Deskripsi**: Verifikasi JWT token (untuk internal service)
**Request Body**:
```json
{
  "token": "jwt_token_here"
}
```

### PUT /auth/token-balance
**Deskripsi**: Update saldo token user (internal service only)
**Request Body**:
```json
{
  "userId": "uuid",
  "amount": 10,
  "operation": "add"
}
```

### GET /auth/batch/registration/stats
**Deskripsi**: Mendapatkan statistik batch registration (internal service only)
**Response**: Batch registration statistics

### POST /auth/batch/registration/process
**Deskripsi**: Force process registration batch queue (internal service only)
**Response**: Batch processing result

### POST /auth/batch/registration/clear
**Deskripsi**: Clear registration batch queue (internal service only)
**Response**: Queue clear result

---

# Assessment Service API

## Main Endpoints

### POST /assessments/submit
**Deskripsi**: Submit data assessment untuk dianalisis AI
**Authentication**: Required
**Token Cost**: 1 token

**Request Body**:
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 70,
    "enterprising": 80,
    "conventional": 65
  },
  "ocean": {
    "openness": 85,
    "conscientiousness": 75,
    "extraversion": 70,
    "agreeableness": 80,
    "neuroticism": 40
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 90,
    "judgment": 75,
    "loveOfLearning": 80,
    "perspective": 70,
    "bravery": 65,
    "perseverance": 85,
    "honesty": 90,
    "zest": 75,
    "love": 80,
    "kindness": 85,
    "socialIntelligence": 70,
    "teamwork": 75,
    "fairness": 85,
    "leadership": 80,
    "forgiveness": 70,
    "humility": 75,
    "prudence": 80,
    "selfRegulation": 75,
    "appreciationOfBeauty": 70,
    "gratitude": 85,
    "hope": 80,
    "humor": 75,
    "spirituality": 60
  }
}
```

**Response** (202):
```json
{
  "success": true,
  "message": "Assessment submitted successfully and queued for analysis",
  "data": {
    "jobId": "uuid-string",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 3,
    "tokenCost": 1,
    "remainingTokens": 9
  }
}
```

### GET /assessments/status/:jobId
**Deskripsi**: Cek status pemrosesan assessment
**Authentication**: Required
**Parameters**: 
- `jobId` (path): UUID job yang dikembalikan dari submit

**Response** (200):
```json
{
  "success": true,
  "message": "Job status retrieved successfully",
  "data": {
    "jobId": "uuid-string",
    "status": "processing",
    "progress": 25,
    "estimatedTimeRemaining": "1-3 minutes",
    "createdAt": "2024-01-01T10:00:00.000Z",
    "updatedAt": "2024-01-01T10:01:00.000Z"
  }
}
```

**Status Values**:
- `queued`: Dalam antrian
- `processing`: Sedang diproses
- `completed`: Selesai (hasil tersedia di archive-service)
- `failed`: Gagal diproses

### GET /assessments/queue/status
**Deskripsi**: Mendapatkan status antrian untuk monitoring
**Authentication**: Required

## Health Check Endpoints

### GET /health
**Deskripsi**: Status kesehatan service dan dependencies
**Authentication**: None

### GET /health/ready
**Deskripsi**: Readiness probe untuk container orchestration
**Authentication**: None

## Development/Test Endpoints

### POST /test/submit
**Deskripsi**: Submit assessment untuk testing (development only)
**Authentication**: None
**Environment**: Development only

### GET /test/status/:jobId
**Deskripsi**: Cek status test assessment
**Authentication**: None
**Environment**: Development only

## Root Endpoint

### GET /
**Deskripsi**: Service info
**Authentication**: None
**Response** (200):
```json
{
  "success": true,
  "message": "ATMA Assessment Service is running",
  "version": "1.0.0",
  "timestamp": "2024-01-01T10:00:00.000Z"
}
```

---

# Archive Service API

## User Endpoints (Perlu authentication)

### GET /archive/results
**Deskripsi**: Mendapatkan hasil analisis user dengan pagination
**Query Parameters**:
- `page`: Halaman (default: 1)
- `limit`: Jumlah per halaman (default: 10, max: 100)
- `status`: Filter status (completed/processing/failed)
- `sort`: Sort by (created_at/updated_at, default: created_at)
- `order`: Sort order (asc/desc, default: desc)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "user_id": "uuid",
      "persona_profile": {
        "archetype": "The Innovator",
        "shortSummary": "Creative problem solver...",
        "strengths": ["Creative", "Analytical", "Leadership"],
        "weaknesses": ["Impatient", "Perfectionist"],
        "careerRecommendation": [
          {
            "careerName": "Software Engineer",
            "careerProspect": {
              "jobAvailability": "high",
              "salaryPotential": "high",
              "careerProgression": "high",
              "industryGrowth": "super high",
              "skillDevelopment": "high"
            }
          }
        ],
        "insights": ["You excel at...", "Consider developing..."],
        "workEnvironment": "Dynamic, collaborative environment",
        "roleModel": ["Elon Musk", "Steve Jobs", "Bill Gates", "Mark Zuckerberg"]
      },
      "status": "completed",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1
  }
}
```

### GET /archive/results/:id
**Deskripsi**: Mendapatkan hasil analisis spesifik berdasarkan ID
**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "persona_profile": {
      "archetype": "The Innovator",
      "shortSummary": "Creative problem solver...",
      "strengths": ["Creative", "Analytical", "Leadership"],
      "weaknesses": ["Impatient", "Perfectionist"],
      "careerRecommendation": [
        {
          "careerName": "Software Engineer",
          "careerProspect": {
            "jobAvailability": "high",
            "salaryPotential": "high",
            "careerProgression": "high",
            "industryGrowth": "super high",
            "skillDevelopment": "high"
          }
        }
      ],
      "insights": ["You excel at...", "Consider developing..."],
      "workEnvironment": "Dynamic, collaborative environment",
      "roleModel": ["Elon Musk", "Steve Jobs", "Bill Gates", "Mark Zuckerberg"]
    },
    "status": "completed",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### PUT /archive/results/:id
**Deskripsi**: Update hasil analisis (user atau internal service)
**Request Body**:
```json
{
  "persona_profile": {
    "archetype": "The Updated Innovator",
    "shortSummary": "Updated summary...",
    "strengths": ["Creative", "Analytical", "Leadership"],
    "weaknesses": ["Impatient", "Perfectionist"],
    "careerRecommendation": [
      {
        "careerName": "Software Engineer",
        "careerProspect": {
          "jobAvailability": "high",
          "salaryPotential": "high",
          "careerProgression": "high",
          "industryGrowth": "super high",
          "skillDevelopment": "high"
        }
      }
    ],
    "insights": ["Updated insights..."],
    "workEnvironment": "Updated work environment",
    "roleModel": ["Updated role models"]
  },
  "status": "completed"
}
```

### DELETE /archive/results/:id
**Deskripsi**: Hapus hasil analisis (user only)
**Response**:
```json
{
  "success": true,
  "message": "Analysis result deleted successfully"
}
```

### GET /archive/stats
**Deskripsi**: Mendapatkan statistik user
**Response**:
```json
{
  "success": true,
  "data": {
    "total_results": 5,
    "completed_results": 4,
    "processing_results": 1,
    "failed_results": 0,
    "latest_result": {
      "id": "uuid",
      "archetype": "The Innovator",
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

### GET /archive/stats/overview
**Deskripsi**: Mendapatkan overview statistik user
**Response**:
```json
{
  "success": true,
  "data": {
    "total_assessments": 5,
    "latest_archetype": "The Innovator",
    "assessment_history": [
      {
        "date": "2024-01-01",
        "archetype": "The Innovator",
        "status": "completed"
      }
    ]
  }
}
```

## Internal Service Endpoints

### POST /archive/results
**Deskripsi**: Buat hasil analisis baru (internal service only)
**Query Parameters**:
- `batch`: true/false (default: true) - Gunakan batch processing atau tidak

**Request Body**:
```json
{
  "user_id": "uuid",
  "assessment_data": {
    "riasec": {...},
    "ocean": {...},
    "viaIs": {...}
  },
  "persona_profile": {
    "archetype": "The Innovator",
    "shortSummary": "Creative problem solver...",
    "strengths": ["Creative", "Analytical", "Leadership"],
    "weaknesses": ["Impatient", "Perfectionist"],
    "careerRecommendation": [
      {
        "careerName": "Software Engineer",
        "careerProspect": {
          "jobAvailability": "high",
          "salaryPotential": "high",
          "careerProgression": "high",
          "industryGrowth": "super high",
          "skillDevelopment": "high"
        }
      }
    ],
    "insights": ["You excel at...", "Consider developing..."],
    "workEnvironment": "Dynamic, collaborative environment",
    "roleModel": ["Elon Musk", "Steve Jobs", "Bill Gates", "Mark Zuckerberg"]
  },
  "status": "completed"
}
```

### POST /archive/results/batch
**Deskripsi**: Buat multiple hasil analisis sekaligus (internal service only)
**Request Body**: Array of analysis result objects

### GET /archive/stats/summary
**Deskripsi**: Mendapatkan summary statistik keseluruhan (internal service only)
**Response**:
```json
{
  "success": true,
  "data": {
    "overall": {
      "total_results": 1000,
      "total_users": 500,
      "completed_results": 950,
      "processing_results": 30,
      "failed_results": 20,
      "success_rate": 95.0
    },
    "top_archetypes": [
      {
        "archetype": "The Innovator",
        "count": 150
      }
    ],
    "recent_activity": {
      "results_last_30_days": 100,
      "active_users_last_30_days": 80
    }
  }
}
```

## Batch Processing Endpoints (Internal Service Only)

### GET /archive/batch/stats
**Deskripsi**: Mendapatkan statistik batch processing
**Response**:
```json
{
  "success": true,
  "data": {
    "queue_size": 5,
    "processing": false,
    "last_batch_processed": "2024-01-01T00:00:00Z",
    "total_processed": 1000
  }
}
```

### POST /archive/batch/process
**Deskripsi**: Force process batch queue
**Response**:
```json
{
  "success": true,
  "message": "Batch processing completed",
  "data": {
    "processed_items": 5,
    "successful": 4,
    "failed": 1,
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

### POST /archive/batch/clear
**Deskripsi**: Clear batch queue (emergency operation)
**Response**:
```json
{
  "success": true,
  "message": "Batch queue cleared successfully",
  "data": {
    "cleared_items": 10,
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

## Demographics Endpoints (Internal Service Only)

### GET /archive/demographics/overview
**Deskripsi**: Mendapatkan overview demografis keseluruhan
**Response**: Demographic overview data

### GET /archive/demographics/archetype/:archetype
**Deskripsi**: Mendapatkan demografis untuk archetype tertentu
**Response**: Archetype-specific demographic data

### GET /archive/demographics/schools
**Deskripsi**: Mendapatkan analitik berdasarkan sekolah
**Query Parameters**:
- `school`: Filter nama sekolah (optional)
**Response**: School-based analytics data

### GET /archive/demographics/optimized
**Deskripsi**: Mendapatkan demografis dengan optimized query
**Query Parameters**:
- `gender`: Filter gender
- `ageMin`: Umur minimum
- `ageMax`: Umur maksimum
- `schoolOrigin`: Filter asal sekolah
- `archetype`: Filter archetype
- `limit`: Limit hasil (default: 100)
**Response**: Optimized demographic data

### GET /archive/demographics/trends
**Deskripsi**: Mendapatkan trend demografis dari waktu ke waktu
**Query Parameters**:
- `period`: Periode waktu (day/week/month/year)
- `limit`: Jumlah periode yang diambil
**Response**: Demographic trends data

## Admin Endpoints

### GET /admin/users
**Deskripsi**: Mendapatkan semua user dengan pagination dan filtering
**Query Parameters**:
- `page`: Halaman (default: 1)
- `limit`: Jumlah per halaman (default: 10, max: 100)
- `search`: Search term untuk email
- `sortBy`: Sort by field (email/token_balance/created_at/updated_at, default: created_at)
- `sortOrder`: Sort order (ASC/DESC, default: DESC)
**Response**: Paginated user list with details

### GET /admin/users/:userId
**Deskripsi**: Mendapatkan detail user berdasarkan ID
**Response**: Detailed user information

### PUT /admin/users/:userId/token-balance
**Deskripsi**: Update saldo token user
**Request Body**:
```json
{
  "token_balance": 100,
  "action": "set"
}
```
**Response**: Updated token balance information

### DELETE /admin/users/:userId
**Deskripsi**: Hapus user (soft delete)
**Response**:
```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

---

## Validation Rules

### User Registration (Auth Service)
- **email**: Valid email format, max 255 characters
- **password**: Min 8 characters, must contain letters and numbers

### User Profile Update (Auth Service)
- **full_name**: Max 100 characters (optional)
- **school_origin**: Max 150 characters (optional)
- **date_of_birth**: Valid date, cannot be in future (optional)
- **gender**: One of: male, female, other, prefer_not_to_say (optional)

### Admin Registration (Auth Service)
- **username**: Alphanumeric, 3-100 characters
- **email**: Valid email format, max 255 characters
- **password**: Min 8 characters, must contain uppercase, lowercase, number, and special character
- **full_name**: Max 255 characters (optional)
- **role**: One of: admin, superadmin, moderator (default: admin)

### Assessment Data (Assessment Service)
- **RIASEC**: 6 dimensi (realistic, investigative, artistic, social, enterprising, conventional)
- **OCEAN**: 5 dimensi (openness, conscientiousness, extraversion, agreeableness, neuroticism)
- **VIA-IS**: 24 character strengths (creativity, curiosity, judgment, dll.)
- **Score Range**: 0-100 (integer)
- **All fields required**: Semua field dalam setiap assessment wajib diisi

### Create Analysis Result (Archive Service)
- **user_id**: Valid UUID (required)
- **assessment_data**: Object with assessment data (optional, already validated in assessment-service)
- **persona_profile**: Complete persona profile object (required)
  - **archetype**: String (required)
  - **shortSummary**: String (required)
  - **strengths**: Array of 3-5 strings (required)
  - **weaknesses**: Array of 3-5 strings (required)
  - **careerRecommendation**: Array of 3-5 career objects (required)
  - **insights**: Array of 3-5 strings (required)
  - **workEnvironment**: String (required)
  - **roleModel**: Array of 4-5 strings (required)
- **status**: One of: completed, processing, failed (default: completed)

### Query Parameters (Archive Service)
- **page**: Integer, min 1 (default: 1)
- **limit**: Integer, min 1, max 100 (default: 10)
- **status**: One of: completed, processing, failed (optional)
- **sort**: One of: created_at, updated_at (default: created_at)
- **order**: One of: asc, desc, ASC, DESC (default: desc)

### Career Prospect Values
All career prospect fields accept these values:
- super high
- high
- moderate
- low
- super low

---

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "string",
  "data": object
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": object
  }
}
```

---

## Important Notes

### Security & Authentication
1. **Rate Limiting**: Auth endpoints memiliki rate limiting untuk keamanan
2. **JWT Tokens**: Digunakan untuk authentication dengan expiration time
3. **Role-based Access**: Admin endpoints memiliki role-based access control
4. **Service Authentication**: Internal service endpoints memerlukan service authentication middleware

### Performance & Processing
1. **Batch Processing**: User registration dan archive service mendukung batch processing untuk performa yang lebih baik
2. **Queue System**: Assessment service menggunakan RabbitMQ untuk queue management
3. **Token Management**: Assessment memerlukan token balance, 1 token per assessment
4. **Pagination**: Endpoint yang mengembalikan list menggunakan pagination dengan format standar

### Data Management
1. **Soft Delete**: User deletion menggunakan soft delete untuk data integrity
2. **Demographics**: Demographics endpoints hanya untuk internal service dan analytics
3. **Validation**: Semua endpoint memiliki validasi input menggunakan Joi schema
4. **Error Handling**: Semua error mengikuti format response yang konsisten

### Development & Testing
1. **Health Checks**: Semua service memiliki health check endpoints untuk monitoring
2. **Test Endpoints**: Assessment service memiliki test endpoints untuk development
3. **Environment**: Beberapa endpoint hanya tersedia di development environment
4. **Password Security**: Admin passwords memiliki requirement yang lebih ketat

### Service Communication
1. **Microservices**: Sistem terdiri dari 3 microservices yang saling berkomunikasi
2. **Internal APIs**: Service-to-service communication menggunakan internal endpoints
3. **Token Verification**: Auth service menyediakan token verification untuk service lain
4. **Data Flow**: Assessment → AI Processing → Archive untuk menyimpan hasil

---

## Quick Start Guide

### 1. User Registration & Login
```bash
# Register new user
POST /auth/register
{
  "email": "<EMAIL>",
  "password": "password123"
}

# Login
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 2. Submit Assessment
```bash
# Submit assessment (requires token)
POST /assessments/submit
Authorization: Bearer <jwt_token>
{
  "riasec": {...},
  "ocean": {...},
  "viaIs": {...}
}
```

### 3. Check Results
```bash
# Check processing status
GET /assessments/status/:jobId
Authorization: Bearer <jwt_token>

# Get completed results
GET /archive/results
Authorization: Bearer <jwt_token>
```

### 4. Admin Access
```bash
# Admin login
POST /admin/login
{
  "username": "admin",
  "password": "admin123"
}

# Manage users
GET /admin/users
Authorization: Bearer <admin_jwt_token>
```
